2025-07-09 14:45:04.783 -07:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-07-09 14:45:04.882 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Accounts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Accounts" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "EmailAddress" TEXT NOT NULL,
    "ImapServer" TEXT NOT NULL,
    "ImapPort" INTEGER NOT NULL,
    "UseSsl" INTEGER NOT NULL,
    "Username" TEXT NOT NULL,
    "Password" TEXT NOT NULL,
    "IsEnabled" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "LastSyncAt" TEXT NOT NULL
);
2025-07-09 14:45:04.883 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Folders" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Folders" PRIMARY KEY AUTOINCREMENT,
    "AccountId" INTEGER NOT NULL,
    "Name" TEXT NOT NULL,
    "FullName" TEXT NOT NULL,
    "ParentFolderId" INTEGER NULL,
    "Type" INTEGER NOT NULL,
    "UnreadCount" INTEGER NOT NULL,
    "TotalCount" INTEGER NOT NULL,
    "LastSyncAt" TEXT NOT NULL,
    CONSTRAINT "FK_Folders_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_Folders_Folders_ParentFolderId" FOREIGN KEY ("ParentFolderId") REFERENCES "Folders" ("Id") ON DELETE RESTRICT
);
2025-07-09 14:45:04.883 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Messages" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Messages" PRIMARY KEY AUTOINCREMENT,
    "MessageId" TEXT NOT NULL,
    "AccountId" INTEGER NOT NULL,
    "FolderId" INTEGER NULL,
    "Subject" TEXT NOT NULL,
    "FromAddress" TEXT NOT NULL,
    "FromName" TEXT NOT NULL,
    "ToAddresses" TEXT NOT NULL,
    "CcAddresses" TEXT NOT NULL,
    "BccAddresses" TEXT NOT NULL,
    "DateSent" TEXT NOT NULL,
    "DateReceived" TEXT NOT NULL,
    "TextBody" TEXT NOT NULL,
    "HtmlBody" TEXT NOT NULL,
    "IsRead" INTEGER NOT NULL,
    "IsFlagged" INTEGER NOT NULL,
    "IsDeleted" INTEGER NOT NULL,
    "Size" INTEGER NOT NULL,
    "HasAttachments" INTEGER NOT NULL,
    CONSTRAINT "FK_Messages_Accounts_AccountId" FOREIGN KEY ("AccountId") REFERENCES "Accounts" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_Messages_Folders_FolderId" FOREIGN KEY ("FolderId") REFERENCES "Folders" ("Id") ON DELETE SET NULL
);
2025-07-09 14:45:04.884 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Attachments" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Attachments" PRIMARY KEY AUTOINCREMENT,
    "MessageId" INTEGER NOT NULL,
    "FileName" TEXT NOT NULL,
    "ContentType" TEXT NOT NULL,
    "Size" INTEGER NOT NULL,
    "ContentId" TEXT NOT NULL,
    "IsInline" INTEGER NOT NULL,
    "Data" BLOB NULL,
    "FilePath" TEXT NULL,
    "CreatedAt" TEXT NOT NULL,
    CONSTRAINT "FK_Attachments_Messages_MessageId" FOREIGN KEY ("MessageId") REFERENCES "Messages" ("Id") ON DELETE CASCADE
);
2025-07-09 14:45:04.884 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Accounts_EmailAddress" ON "Accounts" ("EmailAddress");
2025-07-09 14:45:04.884 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Attachments_MessageId" ON "Attachments" ("MessageId");
2025-07-09 14:45:04.884 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Folders_AccountId_FullName" ON "Folders" ("AccountId", "FullName");
2025-07-09 14:45:04.884 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Folders_ParentFolderId" ON "Folders" ("ParentFolderId");
2025-07-09 14:45:04.884 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Messages_AccountId_MessageId" ON "Messages" ("AccountId", "MessageId");
2025-07-09 14:45:04.884 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Messages_FolderId" ON "Messages" ("FolderId");
2025-07-09 14:45:04.895 -07:00 [INF] Starting sync service
2025-07-09 14:45:04.898 -07:00 [INF] Starting background sync
2025-07-09 14:45:04.905 -07:00 [ERR] Background sync failed
System.InvalidOperationException: An attempt was made to use the context instance while it is being configured. A DbContext instance cannot be used inside 'OnConfiguring' since it is still being configured at this point. This can happen if a second operation is started on this context instance before a previous operation completed. Any instance members are not guaranteed to be thread safe.
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_Model()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.get_EntityType()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.CheckState()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.get_EntityQueryable()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.System.Linq.IQueryable.get_Provider()
   at System.Linq.Queryable.Where[TSource](IQueryable`1 source, Expression`1 predicate)
   at EmailClient.Services.EmailService.SyncAllAccountsAsync() in C:\Users\<USER>\dev\windows app\EmailClient\Services\EmailService.cs:line 182
   at EmailClient.Services.SyncService.SyncCallback() in C:\Users\<USER>\dev\windows app\EmailClient\Services\SyncService.cs:line 76
2025-07-09 14:45:05.037 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 14:45:05.105 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 14:45:05.109 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 14:45:05.129 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 14:45:05.148 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 14:57:52.029 -07:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 14:57:52.315 -07:00 [INF] Starting sync service
2025-07-09 14:57:52.322 -07:00 [INF] Starting background sync
2025-07-09 14:57:52.325 -07:00 [ERR] Failed to initialize main window
System.InvalidOperationException: An attempt was made to use the context instance while it is being configured. A DbContext instance cannot be used inside 'OnConfiguring' since it is still being configured at this point. This can happen if a second operation is started on this context instance before a previous operation completed. Any instance members are not guaranteed to be thread safe.
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_Model()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.get_EntityType()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.CheckState()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.get_EntityQueryable()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.System.Linq.IQueryable.get_Provider()
   at System.Linq.Queryable.OrderBy[TSource,TKey](IQueryable`1 source, Expression`1 keySelector)
   at EmailClient.Services.AccountService.GetAllAccountsAsync() in C:\Users\<USER>\dev\windows app\EmailClient\Services\AccountService.cs:line 23
   at EmailClient.ViewModels.MainWindowViewModel.LoadFolderTreeAsync() in C:\Users\<USER>\dev\windows app\EmailClient\ViewModels\MainWindowViewModel.cs:line 92
   at EmailClient.ViewModels.MainWindowViewModel.InitializeAsync() in C:\Users\<USER>\dev\windows app\EmailClient\ViewModels\MainWindowViewModel.cs:line 79
2025-07-09 14:57:52.530 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
WHERE "a"."IsEnabled"
2025-07-09 14:57:52.641 -07:00 [INF] Background sync completed successfully
2025-07-09 15:00:15.684 -07:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 15:00:15.901 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 15:00:15.969 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 15:00:15.972 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 15:00:15.995 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 15:00:16.016 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 15:07:26.869 -07:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 15:07:27.028 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 15:07:27.090 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 15:07:27.092 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 15:07:27.113 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 15:07:27.131 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 15:09:18.690 -07:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 15:09:18.855 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 15:09:18.920 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 15:09:18.923 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 15:09:18.944 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 15:09:18.963 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 15:17:49.381 -07:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 15:17:49.540 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 15:17:49.601 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 15:17:49.603 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 15:17:49.623 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 15:17:49.642 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 15:19:45.100 -07:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
