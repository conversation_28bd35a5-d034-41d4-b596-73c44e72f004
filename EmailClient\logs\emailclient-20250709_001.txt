2025-07-09 15:27:32.873 -07:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-09 15:27:33.307 -07:00 [ERR] Unhandled dispatcher exception occurred
System.NullReferenceException: Object reference not set to an instance of an object.
   at System.DefaultBinder.BindToMethod(BindingFlags bindingAttr, MethodBase[] match, Object[]& args, ParameterModifier[] modifiers, CultureInfo cultureInfo, String[] names, Object& state)
   at MS.Internal.Xaml.Runtime.DynamicMethodRuntime.CreateInstanceWithCtor(Type type, Object[] args)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CreateInstance(XamlType xamlType, Object[] args)
   at System.Xaml.XamlObjectWriter.Logic_CreateAndAssignToParentStart(ObjectWriterContext ctx)
   at System.Xaml.XamlObjectWriter.WriteStartMember(XamlMember property)
   at System.Windows.Markup.WpfXamlLoader.TransformNodes(XamlReader xamlReader, XamlObjectWriter xamlWriter, Boolean onlyLoadOneNode, Boolean skipJournaledProperties, Boolean shouldPassLineNumberInfo, IXamlLineInfo xamlLineInfo, IXamlLineInfoConsumer xamlLineInfoConsumer, XamlContextStack`1 stack, IStyleConnector styleConnector)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadBamlStreamWithSyncInfo(Stream stream, ParserContext pc)
   at System.Windows.Application.DoStartup()
   at System.Windows.Application.<.ctor>b__1_0(Object unused)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
2025-07-09 15:27:34.058 -07:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
ORDER BY "a"."Name"
2025-07-09 15:27:34.125 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
2025-07-09 15:27:34.129 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0 AND NOT ("m"."IsRead")
2025-07-09 15:27:34.152 -07:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "t"."Id", "t"."AccountId", "t"."BccAddresses", "t"."CcAddresses", "t"."DateReceived", "t"."DateSent", "t"."FolderId", "t"."FromAddress", "t"."FromName", "t"."HasAttachments", "t"."HtmlBody", "t"."IsDeleted", "t"."IsFlagged", "t"."IsRead", "t"."MessageId", "t"."Size", "t"."Subject", "t"."TextBody", "t"."ToAddresses", "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username", "t"."Id0", "t"."AccountId0", "t"."FullName", "t"."LastSyncAt", "t"."Name", "t"."ParentFolderId", "t"."TotalCount", "t"."Type", "t"."UnreadCount"
FROM (
    SELECT "m"."Id", "m"."AccountId", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id" AS "Id0", "f"."AccountId" AS "AccountId0", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
    FROM "Messages" AS "m"
    LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
    WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
    ORDER BY "m"."DateReceived" DESC
    LIMIT @__p_1 OFFSET @__p_0
) AS "t"
INNER JOIN "Accounts" AS "a" ON "t"."AccountId" = "a"."Id"
ORDER BY "t"."DateReceived" DESC
2025-07-09 15:27:34.172 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."AccountId", "m"."Id", "m"."BccAddresses", "m"."CcAddresses", "m"."DateReceived", "m"."DateSent", "m"."FolderId", "m"."FromAddress", "m"."FromName", "m"."HasAttachments", "m"."HtmlBody", "m"."IsDeleted", "m"."IsFlagged", "m"."IsRead", "m"."MessageId", "m"."Size", "m"."Subject", "m"."TextBody", "m"."ToAddresses", "f"."Id", "f"."AccountId", "f"."FullName", "f"."LastSyncAt", "f"."Name", "f"."ParentFolderId", "f"."TotalCount", "f"."Type", "f"."UnreadCount"
FROM "Messages" AS "m"
LEFT JOIN "Folders" AS "f" ON "m"."FolderId" = "f"."Id"
WHERE NOT ("m"."IsDeleted") AND "f"."Type" = 0
ORDER BY "m"."AccountId"
2025-07-09 15:27:35.323 -07:00 [INF] Starting sync service
2025-07-09 15:27:35.325 -07:00 [INF] Starting background sync
2025-07-09 15:27:35.368 -07:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."EmailAddress", "a"."ImapPort", "a"."ImapServer", "a"."IsEnabled", "a"."LastSyncAt", "a"."Name", "a"."Password", "a"."UseSsl", "a"."Username"
FROM "Accounts" AS "a"
WHERE "a"."IsEnabled"
2025-07-09 15:27:35.369 -07:00 [INF] Background sync completed successfully
