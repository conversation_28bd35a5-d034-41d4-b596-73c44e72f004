using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using EmailClient.Data;
using EmailClient.Services;
using EmailClient.ViewModels;
using EmailClient.Views;
using Serilog;
using System.Windows;

namespace EmailClient;

public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.File("logs/emailclient-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        // Build the host
        _host = Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                // Database - Use scoped to avoid threading issues
                services.AddDbContext<EmailDbContext>(options =>
                    options.UseSqlite("Data Source=emailclient.db"), ServiceLifetime.Scoped);

                // Services - Use scoped for services that use DbContext
                services.AddSingleton<IImapService, ImapService>();
                services.AddScoped<IEmailService, EmailService>();
                services.AddScoped<IAccountService, AccountService>();
                services.AddSingleton<ISettingsService, SettingsService>();
                services.AddScoped<ISyncService, SyncService>();
                services.AddScoped<IMultiAccountService, MultiAccountService>();

                // ViewModels - Use scoped to match service lifetimes
                services.AddScoped<MainWindowViewModel>();
                services.AddScoped<AccountSetupViewModel>();
                services.AddScoped<AccountDashboardViewModel>();
                services.AddScoped<SettingsViewModel>();
                services.AddScoped<EmailListViewModel>();
                services.AddScoped<EmailDetailViewModel>();

                // Views
                services.AddTransient<MainWindow>();
            })
            .Build();

        // Ensure database is created
        using (var scope = _host.Services.CreateScope())
        {
            var context = scope.ServiceProvider.GetRequiredService<EmailDbContext>();
            context.Database.EnsureCreated();
        }

        // Start the main window
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        // Start the sync service after the UI is loaded
        _ = Task.Run(async () =>
        {
            await Task.Delay(2000); // Wait 2 seconds for UI to fully load
            using var scope = _host.Services.CreateScope();
            var syncService = scope.ServiceProvider.GetRequiredService<ISyncService>();
            await syncService.StartAsync();
        });

        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        // Stop the sync service
        if (_host != null)
        {
            try
            {
                using var scope = _host.Services.CreateScope();
                var syncService = scope.ServiceProvider.GetService<ISyncService>();
                if (syncService != null)
                {
                    await syncService.StopAsync();
                }
            }
            catch
            {
                // Ignore errors during shutdown
            }
        }

        _host?.Dispose();
        Log.CloseAndFlush();
        base.OnExit(e);
    }
}
